.env
env
ecocycle_debug.log
ecocycle.log
ecocycle_cli.log


# Poetry
poetry.lock
.venv/
dist/
.qodo
/ecocycle_v2.5.bak/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Database
*.db
*.sqlite3

# Credentials and Secrets
*credentials*.json
*client_secret*.json
Google Auth Client Secret.json
google-credentials.json

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.pytest_cache/
.tox/
.hypothesis/

# Package management
uv.lock

# Backups
*.bak

# Logs
*.log

# IDE settings
.idea/
.vscode/
*.swp
*.swo

# Cache and temporary files
/weather_cache.json
/users.json
/session.json
/weather_debug_singapore.json
/routes_cache.json
/route_cache.json
/ai_routes.json
.DS_Store
cache/
temp/
*.tmp

# CI/CD artifacts
bandit-report.json
.mypy_cache/
.flake8_cache/

# Virtual environments
venv/
eco_venv/
env/

# Data directories (for CI performance)
data/cache/
data/exports/
data/debug/
visualizations/

# Backup directories
backups/
database_backups/
system_repair_backups/
