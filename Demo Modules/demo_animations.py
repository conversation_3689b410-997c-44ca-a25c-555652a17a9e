#!/usr/bin/env python3
"""
EcoCycle - Animation Demo
Demonstrates the enhanced ASCII art and animation capabilities in the EcoCycle CLI.
"""
import os
import sys
import time
import random

try:
    import utils.ascii_art as ascii_art
    enhanced_ui = True
except ImportError:
    import utils.ascii_art
    enhanced_ui = False

def run_animation_demo():
    """Run a comprehensive demo of all animation features."""
    ascii_art.clear_screen()
    
    # Display the animated header
    ascii_art.display_header()
    
    if not enhanced_ui:
        print("\nWarning: Enhanced UI module not available. Running with basic UI.")
        print("To see all animations, make sure the 'blessed', 'rich', and 'yaspin' packages are installed.")
        time.sleep(2)
    
    # 1. Test section headers
    ascii_art.display_section_header("Animation Demo")
    print("This demo showcases the enhanced UI features of EcoCycle CLI.")
    time.sleep(1)
    
    # 2. Test loading animations
    print("\nDemonstrating loading animations:")
    if hasattr(ascii_art, 'display_loading_animation'):
        ascii_art.display_loading_animation("Loading EcoCycle features", 2.0)
    else:
        ascii_art.display_loading_message("Loading EcoCycle features")
        time.sleep(2)
    
    # 3. Test message types
    print("\nDemonstrating message types:")
    ascii_art.display_success_message("Operation completed successfully")
    time.sleep(0.7)
    
    ascii_art.display_warning_message("This is a warning message")
    time.sleep(0.7)
    
    ascii_art.display_error_message("This is an error message")
    time.sleep(0.7)
    
    ascii_art.display_info_message("This is an informational message")
    time.sleep(1.5)
    
    # 4. Test animated menu
    print("\nDemonstrating menu display:")
    options = [
        "Log a cycling trip",
        "View statistics",
        "Calculate carbon footprint",
        "Weather and route planning",
        "Eco-challenges"
    ]
    
    if hasattr(ascii_art, 'display_animated_menu'):
        ascii_art.display_animated_menu("Main Menu", options)
    else:
        ascii_art.display_menu("Main Menu", options)
    
    time.sleep(2)
    
    # 5. Test data tables
    print("\nDemonstrating data tables:")
    headers = ["Date", "Distance (km)", "CO2 Saved (kg)", "Calories"]
    data = [
        ["2025-04-12", "12.5", "3.1", "450"],
        ["2025-04-10", "8.2", "2.0", "295"],
        ["2025-04-08", "15.0", "3.8", "540"],
        ["2025-04-05", "10.3", "2.6", "370"]
    ]
    
    ascii_art.display_data_table(headers, data, "Recent Cycling Trips")
    time.sleep(2)
    
    # 6. Test progress bars
    print("\nDemonstrating progress bars:")
    
    if hasattr(ascii_art, 'display_animated_progress_bar'):
        ascii_art.display_animated_progress_bar(75, 100, 40, "Carbon Reduction Progress")
    else:
        ascii_art.display_progress_bar(75, 100, 40, "Carbon Reduction Progress")
    
    time.sleep(1.5)
    
    # 7. Test achievement badges
    print("\nDemonstrating achievement badges:")
    
    if hasattr(ascii_art, 'display_achievement_badge'):
        achievement_types = ['distance', 'carbon_saver', 'streak']
        achievement_names = ['Distance Champion', 'Climate Guardian', 'Consistent Cyclist']
        
        for i, achievement_type in enumerate(achievement_types):
            level = random.randint(1, 3)
            ascii_art.display_achievement_badge(
                achievement_type, 
                level, 
                achievement_names[i]
            )
            time.sleep(1)
    else:
        print("✓ Distance Champion - Level 2")
        print("✓ Climate Guardian - Level 3")
        print("✓ Consistent Cyclist - Level 1")
        time.sleep(1.5)
    
    # 8. Test mascot animation
    if hasattr(ascii_art, 'display_mascot_animation'):
        print("\nDemonstrating mascot animation:")
        ascii_art.display_mascot_animation("Remember to cycle for a greener planet!")
        time.sleep(0.5)
    
    # 9. Test social share graphic
    if hasattr(ascii_art, 'create_social_share_graphic'):
        print("\nDemonstrating social share graphics:")
        ascii_art.create_social_share_graphic(
            "EcoCyclist", 
            "Climate Guardian", 
            {
                "Total Trips": 15,
                "Total Distance": "150.5 km",
                "CO2 Saved": "37.5 kg",
                "Calories Burned": 4500
            }
        )
        time.sleep(2)
    
    # 10. Test route animation
    if hasattr(ascii_art, 'animate_route_on_map'):
        print("\nDemonstrating route animation:")
        ascii_art.animate_route_on_map()
        time.sleep(0.5)
    
    # Conclusion
    ascii_art.display_section_header("Demo Complete")
    print("This concludes the demonstration of EcoCycle's enhanced UI features.")
    print("\nThese animations and visual effects make the CLI experience more")
    print("engaging and interactive for users tracking their cycling activities")
    print("and environmental impact.")
    
    input("\nPress Enter to exit the demo...")

if __name__ == "__main__":
    run_animation_demo()