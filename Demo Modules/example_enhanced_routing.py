#!/usr/bin/env python3
"""
Example demonstration of enhanced route calculation for bicycling trips.
This script shows how the improved routing system works in practice.
"""

import os
import sys
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def demonstrate_enhanced_routing():
    """Demonstrate the enhanced route calculation system."""
    print("🚴 Enhanced Route Calculation for Bicycling Trips")
    print("=" * 55)
    
    try:
        from controllers.route_controller import RouteController
        from controllers.weather_controller import WeatherController
        
        # Initialize controllers
        route_controller = RouteController()
        weather_controller = WeatherController()
        
        # Example locations for demonstration
        locations = [
            ("Central Park, New York", "Brooklyn Bridge, New York"),
            ("Golden Gate Park, San Francisco", "Fisherman's Wharf, San Francisco"),
            ("Hyde Park, London", "Tower Bridge, London")
        ]
        
        for i, (start_location, end_location) in enumerate(locations, 1):
            print(f"\n📍 Example {i}: {start_location} → {end_location}")
            print("-" * 50)
            
            # Get coordinates for locations
            start_coords = weather_controller.get_coordinates_for_location(start_location)
            end_coords = weather_controller.get_coordinates_for_location(end_location)
            
            if not start_coords or not end_coords:
                print(f"❌ Could not find coordinates for one or both locations")
                continue
            
            print(f"📍 Start: {start_coords}")
            print(f"📍 End: {end_coords}")
            
            # Define cycling preferences
            cycling_preferences = {
                "avoid_highways": True,
                "prefer_bike_lanes": True,
                "avoid_steep_hills": False,
                "surface_preference": "paved"
            }
            
            print(f"🚴 Cycling Preferences:")
            for key, value in cycling_preferences.items():
                print(f"   • {key.replace('_', ' ').title()}: {value}")
            
            # Calculate route with enhanced system
            print(f"\n🔄 Calculating bicycle route...")
            route_info = route_controller.get_route_info(start_coords, end_coords, cycling_preferences)
            
            # Display results
            print(f"\n📊 Route Results:")
            print(f"   • Distance: {route_info['distance']:.2f} km")
            print(f"   • Duration: {route_info['duration']:.1f} minutes")
            print(f"   • Average Speed: {(route_info['distance'] / (route_info['duration'] / 60)):.1f} km/h")
            print(f"   • Elevation Gain: {route_info.get('elevation', 0)} m")
            
            # Route quality and features
            quality = route_info.get('route_quality', 'unknown')
            quality_emoji = {
                'excellent': '🟢',
                'good': '🟡', 
                'fair': '🟠',
                'poor': '🔴',
                'error': '❌'
            }.get(quality, '⚪')
            
            print(f"   • Route Quality: {quality_emoji} {quality.title()}")
            print(f"   • Data Source: {route_info.get('source', 'unknown').title()}")
            
            # Bicycle-specific features
            if route_info.get('bike_friendly', False):
                print(f"   • 🚴 Bicycle-friendly route")
            if route_info.get('has_bike_lanes', False):
                print(f"   • 🛣️ Includes bike lanes")
            if route_info.get('estimated', False):
                print(f"   • ⚠️ Estimated route (API unavailable)")
            
            # Environmental impact
            distance = route_info['distance']
            co2_saved = distance * 0.192  # kg CO2 saved vs driving
            calories_burned = distance * 50  # approximate calories per km
            
            print(f"\n🌱 Environmental Impact:")
            print(f"   • CO₂ Saved: {co2_saved:.2f} kg")
            print(f"   • Calories Burned: {calories_burned:.0f} kcal")
            print(f"   • Trees Equivalent: {co2_saved / 0.022:.1f} trees/day")
            
    except ImportError as e:
        print(f"❌ Required modules not available: {e}")
        print("Please ensure all dependencies are installed.")
    except Exception as e:
        print(f"❌ Error during demonstration: {e}")
        logger.error(f"Demonstration error: {e}", exc_info=True)

def demonstrate_api_fallback():
    """Demonstrate API fallback behavior."""
    print("\n🔄 API Fallback Demonstration")
    print("=" * 35)
    
    try:
        from controllers.route_controller import RouteController
        
        # Initialize controller
        route_controller = RouteController()
        
        # Test coordinates (short route for quick testing)
        start_coords = (40.7128, -74.0060)  # Manhattan
        end_coords = (40.6782, -73.9442)    # Brooklyn
        
        print(f"📍 Test Route: Manhattan → Brooklyn")
        print(f"   Start: {start_coords}")
        print(f"   End: {end_coords}")
        
        # Test with different preference scenarios
        preference_scenarios = [
            {
                "name": "Bike Lane Preference",
                "prefs": {"avoid_highways": True, "prefer_bike_lanes": True, "avoid_steep_hills": False}
            },
            {
                "name": "Hill Avoidance",
                "prefs": {"avoid_highways": True, "prefer_bike_lanes": False, "avoid_steep_hills": True}
            },
            {
                "name": "Highway Tolerance",
                "prefs": {"avoid_highways": False, "prefer_bike_lanes": True, "avoid_steep_hills": False}
            }
        ]
        
        for scenario in preference_scenarios:
            print(f"\n🚴 Scenario: {scenario['name']}")
            print(f"   Preferences: {scenario['prefs']}")
            
            route_info = route_controller.get_route_info(start_coords, end_coords, scenario['prefs'])
            
            print(f"   Result: {route_info['distance']:.2f} km, {route_info['duration']:.1f} min")
            print(f"   Quality: {route_info.get('route_quality', 'unknown')}")
            print(f"   Source: {route_info.get('source', 'unknown')}")
            
    except Exception as e:
        print(f"❌ Error during fallback demonstration: {e}")
        logger.error(f"Fallback demonstration error: {e}", exc_info=True)

def demonstrate_caching():
    """Demonstrate route caching functionality."""
    print("\n💾 Route Caching Demonstration")
    print("=" * 32)
    
    try:
        from controllers.route_controller import RouteController
        import time
        
        # Initialize controller
        route_controller = RouteController()
        
        # Test coordinates
        start_coords = (40.7128, -74.0060)
        end_coords = (40.6782, -73.9442)
        
        preferences = {"avoid_highways": True, "prefer_bike_lanes": True}
        
        print(f"📍 Test Route: {start_coords} → {end_coords}")
        print(f"🚴 Preferences: {preferences}")
        
        # First calculation (should hit API or calculate)
        print(f"\n🔄 First calculation (fresh)...")
        start_time = time.time()
        route_info_1 = route_controller.get_route_info(start_coords, end_coords, preferences)
        first_time = time.time() - start_time
        
        print(f"   Time: {first_time:.3f} seconds")
        print(f"   Source: {route_info_1.get('source', 'unknown')}")
        print(f"   Distance: {route_info_1['distance']:.2f} km")
        
        # Second calculation (should hit cache)
        print(f"\n💾 Second calculation (cached)...")
        start_time = time.time()
        route_info_2 = route_controller.get_route_info(start_coords, end_coords, preferences)
        second_time = time.time() - start_time
        
        print(f"   Time: {second_time:.3f} seconds")
        print(f"   Source: {route_info_2.get('source', 'unknown')}")
        print(f"   Distance: {route_info_2['distance']:.2f} km")
        
        # Compare results
        if route_info_1['distance'] == route_info_2['distance']:
            print(f"✅ Cache working correctly - identical results")
            print(f"⚡ Speed improvement: {((first_time - second_time) / first_time * 100):.1f}%")
        else:
            print(f"⚠️ Cache may not be working - different results")
            
    except Exception as e:
        print(f"❌ Error during caching demonstration: {e}")
        logger.error(f"Caching demonstration error: {e}", exc_info=True)

def main():
    """Run all demonstrations."""
    print("🚴 EcoCycle Enhanced Route Calculation Demo")
    print("=" * 45)
    print("This demonstration shows the improved bicycle routing")
    print("system for the Log Cycling Trip feature.\n")
    
    # Run demonstrations
    demonstrate_enhanced_routing()
    demonstrate_api_fallback()
    demonstrate_caching()
    
    print("\n" + "=" * 45)
    print("✅ Demonstration completed!")
    print("\nThe enhanced route calculation system provides:")
    print("• 🎯 Accurate bicycle-specific routing")
    print("• 🚴 Cycling preference support")
    print("• 🔄 Multiple API fallback system")
    print("• 💾 Intelligent caching")
    print("• 📊 Route quality assessment")
    print("• 🌱 Environmental impact calculation")

if __name__ == "__main__":
    main()
