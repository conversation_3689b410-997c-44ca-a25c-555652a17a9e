#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EcoCycle - Logging Example Module
Demonstrates how to use the logging_manager module.
"""
import time
import random
import logging_manager

# Get a logger for this module
logger = logging_manager.get_logger(__name__)

# Example function with performance monitoring
@logging_manager.log_function_call
def example_function(iterations=5, sleep_time=0.1):
    """
    Example function that demonstrates logging and performance monitoring.
    
    Args:
        iterations: Number of iterations to perform
        sleep_time: Time to sleep between iterations
    
    Returns:
        dict: Results of the operation
    """
    logger.info(f"Starting example function with {iterations} iterations")
    
    results = []
    for i in range(iterations):
        # Log at different levels
        if i == 0:
            logger.debug(f"First iteration (i={i})")
        elif i == iterations - 1:
            logger.debug(f"Last iteration (i={i})")
        
        # Simulate some work
        time.sleep(sleep_time)
        
        # Generate a random value
        value = random.random()
        results.append(value)
        
        # Log based on the value
        if value < 0.3:
            logger.warning(f"Low value detected: {value}")
        elif value > 0.7:
            logger.info(f"High value detected: {value}")
    
    # Simulate an occasional error
    if random.random() < 0.2:  # 20% chance of error
        try:
            # Intentionally cause an error
            result = 1 / 0
        except Exception as e:
            logger.error(f"An error occurred: {str(e)}", exc_info=True)
    
    logger.info(f"Completed example function with {len(results)} results")
    return {"results": results, "average": sum(results) / len(results) if results else 0}

# Example of manual performance monitoring
def manual_performance_example():
    """Demonstrates manual performance monitoring."""
    logger.info("Starting manual performance monitoring example")
    
    # Start a timer
    logging_manager.PerformanceMonitor.start_timer("manual_example")
    
    # Simulate some work
    time.sleep(0.5)
    
    # Stop the timer and get the execution time
    execution_time = logging_manager.PerformanceMonitor.stop_timer("manual_example")
    
    logger.info(f"Manual performance monitoring example completed in {execution_time:.4f}s")

def main():
    """Main function to demonstrate logging features."""
    logger.info("Starting logging example")
    
    # Log at different levels
    logger.debug("This is a debug message")
    logger.info("This is an info message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    
    # Call the example function a few times
    for i in range(3):
        logger.info(f"Calling example_function (iteration {i+1})")
        result = example_function(iterations=3, sleep_time=0.1)
        logger.info(f"Result average: {result['average']:.4f}")
    
    # Demonstrate manual performance monitoring
    manual_performance_example()
    
    # Log performance statistics
    logging_manager.PerformanceMonitor.log_stats(logger)
    
    logger.info("Logging example completed")

if __name__ == "__main__":
    main()