#!/usr/bin/env python3
"""
EcoCycle - Performance and Security Test Runner
Comprehensive test runner for performance testing and security testing.
"""

import os
import sys
import unittest
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Any

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PerformanceSecurityTestRunner:
    """Comprehensive test runner for performance and security tests."""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.results = {
            'performance_tests': {},
            'security_tests': {},
            'load_tests': {},
            'summary': {}
        }
        
        # Test modules to run
        self.test_modules = [
            'Tests.test_performance_comprehensive',
            'Tests.test_security_comprehensive',
        ]
        
        # Load testing module (optional)
        try:
            from Tests.test_load_testing import LoadTestRunner
            self.load_test_runner = LoadTestRunner()
            self.load_testing_available = True
        except ImportError as e:
            logger.warning(f"Load testing not available: {e}")
            self.load_test_runner = None
            self.load_testing_available = False
    
    def run_all_tests(self, include_load_tests=True, load_test_duration=60):
        """Run all performance and security tests."""
        logger.info("Starting comprehensive performance and security testing...")
        self.start_time = time.time()
        
        try:
            # Run unit tests for performance and security
            self._run_unit_tests()
            
            # Run load tests if available and requested
            if include_load_tests and self.load_testing_available:
                self._run_load_tests(duration=load_test_duration)
            
            # Generate summary
            self._generate_summary()
            
        except Exception as e:
            logger.error(f"Error during testing: {e}")
            self.results['error'] = str(e)
        
        finally:
            self.end_time = time.time()
            self.results['total_duration'] = self.end_time - self.start_time
        
        return self.results
    
    def _run_unit_tests(self):
        """Run performance and security unit tests."""
        logger.info("Running performance and security unit tests...")
        
        for module_name in self.test_modules:
            try:
                logger.info(f"Running tests from {module_name}")
                
                # Discover and run tests
                loader = unittest.TestLoader()
                suite = loader.loadTestsFromName(module_name)
                
                # Run tests with custom result collector
                result_collector = TestResultCollector()
                runner = unittest.TextTestRunner(
                    stream=result_collector,
                    verbosity=2,
                    resultclass=CustomTestResult
                )
                
                test_result = runner.run(suite)
                
                # Store results
                test_category = self._get_test_category(module_name)
                self.results[test_category] = {
                    'tests_run': test_result.testsRun,
                    'failures': len(test_result.failures),
                    'errors': len(test_result.errors),
                    'skipped': len(test_result.skipped),
                    'success_rate': self._calculate_success_rate(test_result),
                    'details': result_collector.get_results()
                }
                
                logger.info(f"Completed {module_name}: {test_result.testsRun} tests, "
                          f"{len(test_result.failures)} failures, {len(test_result.errors)} errors")
                
            except Exception as e:
                logger.error(f"Error running {module_name}: {e}")
                test_category = self._get_test_category(module_name)
                self.results[test_category] = {
                    'error': str(e),
                    'tests_run': 0,
                    'success_rate': 0.0
                }
    
    def _run_load_tests(self, duration=60):
        """Run load tests using Locust."""
        logger.info("Running load tests...")
        
        try:
            # Basic load test
            logger.info("Running basic load test...")
            basic_load_result = self.load_test_runner.run_load_test(
                users=10,
                spawn_rate=2,
                run_time=duration // 2
            )
            
            # Stress test
            logger.info("Running stress test...")
            stress_test_result = self.load_test_runner.run_stress_test(
                max_users=50,
                spawn_rate=5,
                run_time=duration // 2
            )
            
            # Store results
            self.results['load_tests'] = {
                'basic_load_test': basic_load_result,
                'stress_test': stress_test_result,
                'test_duration': duration
            }
            
            # Generate load test report
            report_file = os.path.join(PROJECT_ROOT, 'Tests', 'test_logs', 'load_test_report.json')
            self.load_test_runner.generate_load_test_report(basic_load_result, report_file)
            
            logger.info("Load tests completed successfully")
            
        except Exception as e:
            logger.error(f"Error during load testing: {e}")
            self.results['load_tests'] = {
                'error': str(e),
                'test_duration': duration
            }
    
    def _get_test_category(self, module_name):
        """Get test category from module name."""
        if 'performance' in module_name:
            return 'performance_tests'
        elif 'security' in module_name:
            return 'security_tests'
        else:
            return 'other_tests'
    
    def _calculate_success_rate(self, test_result):
        """Calculate success rate from test results."""
        if test_result.testsRun == 0:
            return 0.0
        
        successful_tests = test_result.testsRun - len(test_result.failures) - len(test_result.errors)
        return (successful_tests / test_result.testsRun) * 100
    
    def _generate_summary(self):
        """Generate test summary."""
        logger.info("Generating test summary...")
        
        total_tests = 0
        total_failures = 0
        total_errors = 0
        total_skipped = 0
        
        # Aggregate results
        for category, results in self.results.items():
            if category in ['performance_tests', 'security_tests'] and 'tests_run' in results:
                total_tests += results['tests_run']
                total_failures += results['failures']
                total_errors += results['errors']
                total_skipped += results['skipped']
        
        # Calculate overall metrics
        overall_success_rate = 0.0
        if total_tests > 0:
            successful_tests = total_tests - total_failures - total_errors
            overall_success_rate = (successful_tests / total_tests) * 100
        
        # Performance assessment
        performance_grade = self._assess_performance()
        security_grade = self._assess_security()
        
        self.results['summary'] = {
            'total_tests': total_tests,
            'total_failures': total_failures,
            'total_errors': total_errors,
            'total_skipped': total_skipped,
            'overall_success_rate': overall_success_rate,
            'performance_grade': performance_grade,
            'security_grade': security_grade,
            'load_testing_available': self.load_testing_available,
            'test_timestamp': datetime.now().isoformat(),
            'recommendations': self._generate_recommendations()
        }
    
    def _assess_performance(self):
        """Assess overall performance grade."""
        perf_results = self.results.get('performance_tests', {})
        
        if 'error' in perf_results:
            return 'F - Tests failed to run'
        
        success_rate = perf_results.get('success_rate', 0)
        
        if success_rate >= 90:
            return 'A - Excellent'
        elif success_rate >= 80:
            return 'B - Good'
        elif success_rate >= 70:
            return 'C - Acceptable'
        elif success_rate >= 60:
            return 'D - Needs Improvement'
        else:
            return 'F - Poor'
    
    def _assess_security(self):
        """Assess overall security grade."""
        sec_results = self.results.get('security_tests', {})
        
        if 'error' in sec_results:
            return 'F - Tests failed to run'
        
        success_rate = sec_results.get('success_rate', 0)
        
        if success_rate >= 95:
            return 'A - Excellent'
        elif success_rate >= 85:
            return 'B - Good'
        elif success_rate >= 75:
            return 'C - Acceptable'
        elif success_rate >= 65:
            return 'D - Needs Improvement'
        else:
            return 'F - Poor'
    
    def _generate_recommendations(self):
        """Generate recommendations based on test results."""
        recommendations = []
        
        # Performance recommendations
        perf_results = self.results.get('performance_tests', {})
        if perf_results.get('success_rate', 0) < 80:
            recommendations.append("Improve application performance - consider caching and database optimization")
        
        # Security recommendations
        sec_results = self.results.get('security_tests', {})
        if sec_results.get('success_rate', 0) < 85:
            recommendations.append("Strengthen security measures - review authentication and input validation")
        
        # Load testing recommendations
        if not self.load_testing_available:
            recommendations.append("Install Locust for comprehensive load testing capabilities")
        
        load_results = self.results.get('load_tests', {})
        if 'basic_load_test' in load_results:
            avg_response_time = load_results['basic_load_test'].get('average_response_time', 0)
            if avg_response_time > 1000:
                recommendations.append("Optimize response times - current average exceeds 1 second")
        
        if not recommendations:
            recommendations.append("All tests passed - maintain current quality standards")
        
        return recommendations
    
    def save_results(self, output_file=None):
        """Save test results to file."""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(PROJECT_ROOT, 'Tests', 'test_logs', 
                                     f'performance_security_report_{timestamp}.json')
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        try:
            with open(output_file, 'w') as f:
                json.dump(self.results, f, indent=2, default=str)
            
            logger.info(f"Test results saved to {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"Error saving results: {e}")
            return None
    
    def print_summary(self):
        """Print test summary to console."""
        summary = self.results.get('summary', {})
        
        print("\n" + "="*60)
        print("ECOCYCLE PERFORMANCE & SECURITY TEST SUMMARY")
        print("="*60)
        
        print(f"Total Tests Run: {summary.get('total_tests', 0)}")
        print(f"Failures: {summary.get('total_failures', 0)}")
        print(f"Errors: {summary.get('total_errors', 0)}")
        print(f"Skipped: {summary.get('total_skipped', 0)}")
        print(f"Overall Success Rate: {summary.get('overall_success_rate', 0):.1f}%")
        print(f"Performance Grade: {summary.get('performance_grade', 'Unknown')}")
        print(f"Security Grade: {summary.get('security_grade', 'Unknown')}")
        print(f"Load Testing Available: {summary.get('load_testing_available', False)}")
        
        if summary.get('recommendations'):
            print("\nRecommendations:")
            for i, rec in enumerate(summary['recommendations'], 1):
                print(f"  {i}. {rec}")
        
        print("="*60)

class TestResultCollector:
    """Collects test results for analysis."""
    
    def __init__(self):
        self.results = []
    
    def write(self, text):
        """Capture test output."""
        self.results.append(text)
    
    def flush(self):
        """Flush output."""
        pass
    
    def get_results(self):
        """Get collected results."""
        return ''.join(self.results)

class CustomTestResult(unittest.TestResult):
    """Custom test result class for detailed reporting."""
    
    def __init__(self, stream=None, descriptions=None, verbosity=None):
        super().__init__(stream, descriptions, verbosity)
        self.test_details = []
    
    def addSuccess(self, test):
        super().addSuccess(test)
        self.test_details.append({
            'test': str(test),
            'status': 'PASS',
            'message': None
        })
    
    def addError(self, test, err):
        super().addError(test, err)
        self.test_details.append({
            'test': str(test),
            'status': 'ERROR',
            'message': str(err[1])
        })
    
    def addFailure(self, test, err):
        super().addFailure(test, err)
        self.test_details.append({
            'test': str(test),
            'status': 'FAIL',
            'message': str(err[1])
        })

if __name__ == '__main__':
    # Run comprehensive performance and security tests
    runner = PerformanceSecurityTestRunner()
    
    # Run all tests
    results = runner.run_all_tests(include_load_tests=True, load_test_duration=120)
    
    # Print summary
    runner.print_summary()
    
    # Save results
    output_file = runner.save_results()
    if output_file:
        print(f"\nDetailed results saved to: {output_file}")
    
    # Exit with appropriate code
    summary = results.get('summary', {})
    success_rate = summary.get('overall_success_rate', 0)
    exit_code = 0 if success_rate >= 80 else 1
    sys.exit(exit_code)
