#!/usr/bin/env python3
"""
Basic Functionality Tests
Tests core functionality of the EcoCycle application to ensure basic operations work.
"""

import os
import sys
import unittest
import tempfile
import sqlite3
from datetime import datetime

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

from Tests import EcoCycleTestCase
import core.database_manager as database_manager

class TestBasicFunctionality(EcoCycleTestCase):
    """Basic functionality tests."""
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        
        # Create temporary database file
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        # Override database file for testing
        self.original_db_file = database_manager.DATABASE_FILE
        database_manager.DATABASE_FILE = self.temp_db.name
        
        # Initialize database
        database_manager.initialize_database()
        
    def tearDown(self):
        """Clean up test fixtures."""
        super().tearDown()
        
        # Restore original database file
        database_manager.DATABASE_FILE = self.original_db_file
        
        # Remove temporary database file
        if os.path.exists(self.temp_db.name):
            try:
                os.unlink(self.temp_db.name)
            except OSError:
                pass
    
    def test_database_initialization(self):
        """Test that database initializes correctly."""
        # Verify database file exists
        self.assertTrue(os.path.exists(self.temp_db.name))
        
        # Verify tables were created
        with sqlite3.connect(self.temp_db.name) as conn:
            cursor = conn.cursor()
            
            # Check for required tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = ['users', 'preferences', 'verification_tokens', 'stats', 'trips']
            for table in required_tables:
                self.assertIn(table, tables, f"Table {table} not found in database")
    
    def test_database_connection(self):
        """Test database connection functionality."""
        # Test creating a connection
        conn = database_manager.create_connection()
        self.assertIsNotNone(conn)
        
        if conn:
            # Test basic query
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM users")
            result = cursor.fetchone()
            self.assertIsNotNone(result)
            conn.close()
    
    def test_user_creation(self):
        """Test basic user creation functionality."""
        # Test user data
        user_data = (
            'test_user',           # username
            'Test User',           # name
            '<EMAIL>',    # email
            'hashed_password',     # password_hash
            'salt_value',          # salt
            None,                  # google_id
            0,                     # is_admin
            0,                     # is_guest
            datetime.now().isoformat()  # registration_date
        )
        
        # Create user using database manager function
        with database_manager.get_connection() as conn:
            user_id = database_manager.add_user(conn, user_data)
            
        self.assertIsNotNone(user_id)
        self.assertGreater(user_id, 0)
        
        # Verify user was created
        with database_manager.get_connection() as conn:
            user = database_manager.get_user_by_username(conn, 'test_user')
            
        self.assertIsNotNone(user)
        self.assertEqual(user[1], 'test_user')  # username is at index 1
        self.assertEqual(user[3], '<EMAIL>')  # email is at index 3
    
    def test_trip_creation(self):
        """Test basic trip creation functionality."""
        # First create a user
        user_data = (
            'trip_test_user',
            'Trip Test User',
            '<EMAIL>',
            'hashed_password',
            'salt_value',
            None,
            0,
            0,
            datetime.now().isoformat()
        )
        
        with database_manager.get_connection() as conn:
            user_id = database_manager.add_user(conn, user_data)
        
        # Create a trip
        trip_data = (
            user_id,                    # user_id
            datetime.now().isoformat(), # date
            10.5,                       # distance
            30.0,                       # duration
            2.016,                      # co2_saved
            525                         # calories
        )
        
        with database_manager.get_connection() as conn:
            trip_id = database_manager.add_trip(conn, trip_data)
            
        self.assertIsNotNone(trip_id)
        self.assertGreater(trip_id, 0)
        
        # Verify trip was created
        with database_manager.get_connection() as conn:
            trips = database_manager.get_trips_by_user(conn, user_id)
            
        self.assertEqual(len(trips), 1)
        self.assertEqual(trips[0][2], 10.5)  # distance
        self.assertEqual(trips[0][5], 525)   # calories
    
    def test_statistics_functionality(self):
        """Test basic statistics functionality."""
        # Create a user
        user_data = (
            'stats_test_user',
            'Stats Test User',
            '<EMAIL>',
            'hashed_password',
            'salt_value',
            None,
            0,
            0,
            datetime.now().isoformat()
        )
        
        with database_manager.get_connection() as conn:
            user_id = database_manager.add_user(conn, user_data)
        
        # Create statistics
        stats_data = (
            user_id,  # user_id
            5,        # total_trips
            25.5,     # total_distance
            4.896,    # total_co2_saved
            1275      # total_calories
        )
        
        with database_manager.get_connection() as conn:
            stats_id = database_manager.add_stat(conn, stats_data)
            
        self.assertIsNotNone(stats_id)
        self.assertGreater(stats_id, 0)
        
        # Verify statistics were created
        with database_manager.get_connection() as conn:
            stats = database_manager.get_stats_by_user(conn, user_id)
            
        self.assertIsNotNone(stats)
        self.assertEqual(len(stats), 1)
        self.assertEqual(stats[0][2], 5)     # total_trips
        self.assertEqual(stats[0][3], 25.5)  # total_distance
    
    def test_database_backup_creation(self):
        """Test basic database backup functionality."""
        # Create some test data first
        user_data = (
            'backup_test_user',
            'Backup Test User',
            '<EMAIL>',
            'hashed_password',
            'salt_value',
            None,
            0,
            0,
            datetime.now().isoformat()
        )
        
        with database_manager.get_connection() as conn:
            user_id = database_manager.add_user(conn, user_data)
        
        # Create backup
        backup_file = database_manager.create_backup()
        
        self.assertIsNotNone(backup_file)
        self.assertTrue(os.path.exists(backup_file))
        
        # Verify backup contains data
        with sqlite3.connect(backup_file) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            self.assertGreater(user_count, 0)
    
    def test_error_handling(self):
        """Test basic error handling."""
        # Test invalid user data
        invalid_user_data = ()  # Empty tuple
        
        with database_manager.get_connection() as conn:
            result = database_manager.add_user(conn, invalid_user_data)
            
        # Should return None for invalid data
        self.assertIsNone(result)
        
        # Test querying non-existent user
        with database_manager.get_connection() as conn:
            user = database_manager.get_user_by_username(conn, 'non_existent_user')
            
        self.assertIsNone(user)
    
    def test_database_performance_basic(self):
        """Test basic database performance."""
        import time
        
        # Create a user for testing
        user_data = (
            'perf_test_user',
            'Performance Test User',
            '<EMAIL>',
            'hashed_password',
            'salt_value',
            None,
            0,
            0,
            datetime.now().isoformat()
        )
        
        with database_manager.get_connection() as conn:
            user_id = database_manager.add_user(conn, user_data)
        
        # Test bulk trip creation performance
        start_time = time.time()
        
        for i in range(50):  # Create 50 trips
            trip_data = (
                user_id,
                datetime.now().isoformat(),
                5.0 + i % 10,  # Varying distance
                20.0 + i % 15, # Varying duration
                1.0 + i % 3,   # Varying CO2 saved
                300 + i % 100  # Varying calories
            )
            
            with database_manager.get_connection() as conn:
                database_manager.add_trip(conn, trip_data)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should complete in reasonable time (less than 5 seconds)
        self.assertLess(execution_time, 5.0, f"Bulk insertion took {execution_time:.2f}s, should be under 5s")
        
        # Verify all trips were created
        with database_manager.get_connection() as conn:
            trips = database_manager.get_trips_by_user(conn, user_id)
            
        self.assertEqual(len(trips), 50)

if __name__ == '__main__':
    unittest.main()
