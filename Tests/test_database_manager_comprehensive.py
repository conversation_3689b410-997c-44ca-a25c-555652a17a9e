#!/usr/bin/env python3
"""
Comprehensive Database Manager Tests
Tests all database operations including initialization, CRUD operations,
migrations, backups, and performance optimization.
"""

import os
import sys
import unittest
import tempfile
import sqlite3
import json
from unittest.mock import Mock, patch
from datetime import datetime

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

from Tests import EcoCycleTestCase, get_test_config
import core.database_manager as database_manager

class TestDatabaseManagerComprehensive(EcoCycleTestCase):
    """Comprehensive tests for database manager functionality."""

    def setUp(self):
        """Set up test fixtures."""
        super().setUp()

        # Create temporary database file
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()

        # Override database file for testing
        self.original_db_file = database_manager.DATABASE_FILE
        database_manager.DATABASE_FILE = self.temp_db.name

        # Initialize database
        database_manager.initialize_database()

    def tearDown(self):
        """Clean up test fixtures."""
        super().tearDown()

        # Restore original database file
        database_manager.DATABASE_FILE = self.original_db_file

        # Remove temporary database file
        if os.path.exists(self.temp_db.name):
            try:
                os.unlink(self.temp_db.name)
            except OSError:
                pass

    def test_database_initialization(self):
        """Test database initialization and table creation."""
        # Verify database file exists
        self.assertTrue(os.path.exists(self.temp_db.name))

        # Verify tables were created
        with sqlite3.connect(self.temp_db.name) as conn:
            cursor = conn.cursor()

            # Check for required tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]

            required_tables = ['users', 'preferences', 'verification_tokens', 'stats', 'trips', 'migrations']
            for table in required_tables:
                self.assertIn(table, tables, f"Table {table} not found in database")

    def test_user_operations(self):
        """Test user CRUD operations."""
        self.db_manager.initialize_database()

        # Test user creation
        user_data = {
            'username': 'test_user',
            'name': 'Test User',
            'email': '<EMAIL>',
            'password_hash': 'hashed_password',
            'salt': 'salt_value',
            'is_admin': 0,
            'is_guest': 0,
            'registration_date': datetime.now().isoformat(),
            'email_verified': 0
        }

        user_id = self.db_manager.create_user(user_data)
        self.assertIsNotNone(user_id)
        self.assertGreater(user_id, 0)

        # Test user retrieval
        retrieved_user = self.db_manager.get_user_by_username('test_user')
        self.assertIsNotNone(retrieved_user)
        self.assertEqual(retrieved_user['username'], 'test_user')
        self.assertEqual(retrieved_user['email'], '<EMAIL>')

        # Test user update
        update_data = {'name': 'Updated Test User', 'email_verified': 1}
        result = self.db_manager.update_user(user_id, update_data)
        self.assertTrue(result)

        # Verify update
        updated_user = self.db_manager.get_user_by_id(user_id)
        self.assertEqual(updated_user['name'], 'Updated Test User')
        self.assertEqual(updated_user['email_verified'], 1)

        # Test user deletion
        result = self.db_manager.delete_user(user_id)
        self.assertTrue(result)

        # Verify deletion
        deleted_user = self.db_manager.get_user_by_id(user_id)
        self.assertIsNone(deleted_user)

    def test_trip_operations(self):
        """Test trip CRUD operations."""
        self.db_manager.initialize_database()

        # Create a test user first
        user_data = {
            'username': 'trip_test_user',
            'email': '<EMAIL>',
            'password_hash': 'hash',
            'salt': 'salt',
            'is_admin': 0,
            'is_guest': 0,
            'registration_date': datetime.now().isoformat()
        }
        user_id = self.db_manager.create_user(user_data)

        # Test trip creation
        trip_data = {
            'user_id': user_id,
            'date': datetime.now().isoformat(),
            'distance': 10.5,
            'duration': 30.0,
            'co2_saved': 2.016,
            'calories': 525,
            'route_data': json.dumps({'start': 'Home', 'end': 'Work'}),
            'weather_data': json.dumps({'temp': 22, 'condition': 'sunny'})
        }

        trip_id = self.db_manager.create_trip(trip_data)
        self.assertIsNotNone(trip_id)
        self.assertGreater(trip_id, 0)

        # Test trip retrieval
        retrieved_trip = self.db_manager.get_trip_by_id(trip_id)
        self.assertIsNotNone(retrieved_trip)
        self.assertEqual(retrieved_trip['distance'], 10.5)
        self.assertEqual(retrieved_trip['user_id'], user_id)

        # Test trips by user
        user_trips = self.db_manager.get_trips_by_user(user_id)
        self.assertEqual(len(user_trips), 1)
        self.assertEqual(user_trips[0]['id'], trip_id)

        # Test trip update
        update_data = {'distance': 12.0, 'calories': 600}
        result = self.db_manager.update_trip(trip_id, update_data)
        self.assertTrue(result)

        # Verify update
        updated_trip = self.db_manager.get_trip_by_id(trip_id)
        self.assertEqual(updated_trip['distance'], 12.0)
        self.assertEqual(updated_trip['calories'], 600)

        # Test trip deletion
        result = self.db_manager.delete_trip(trip_id)
        self.assertTrue(result)

        # Verify deletion
        deleted_trip = self.db_manager.get_trip_by_id(trip_id)
        self.assertIsNone(deleted_trip)

    def test_statistics_operations(self):
        """Test statistics tracking and aggregation."""
        self.db_manager.initialize_database()

        # Create test user
        user_data = {
            'username': 'stats_test_user',
            'email': '<EMAIL>',
            'password_hash': 'hash',
            'salt': 'salt',
            'is_admin': 0,
            'is_guest': 0,
            'registration_date': datetime.now().isoformat()
        }
        user_id = self.db_manager.create_user(user_data)

        # Create multiple trips
        trips = [
            {'distance': 5.0, 'co2_saved': 0.96, 'calories': 250, 'duration': 15.0},
            {'distance': 10.0, 'co2_saved': 1.92, 'calories': 500, 'duration': 30.0},
            {'distance': 7.5, 'co2_saved': 1.44, 'calories': 375, 'duration': 22.5}
        ]

        for trip in trips:
            trip_data = {
                'user_id': user_id,
                'date': datetime.now().isoformat(),
                **trip
            }
            self.db_manager.create_trip(trip_data)

        # Test statistics calculation
        stats = self.db_manager.calculate_user_statistics(user_id)
        self.assertIsNotNone(stats)
        self.assertEqual(stats['total_trips'], 3)
        self.assertEqual(stats['total_distance'], 22.5)
        self.assertEqual(stats['total_co2_saved'], 4.32)
        self.assertEqual(stats['total_calories'], 1125)

        # Test statistics update
        result = self.db_manager.update_user_statistics(user_id, stats)
        self.assertTrue(result)

        # Verify statistics were saved
        saved_stats = self.db_manager.get_user_statistics(user_id)
        self.assertIsNotNone(saved_stats)
        self.assertEqual(saved_stats['total_trips'], 3)
        self.assertEqual(saved_stats['total_distance'], 22.5)

    def test_preferences_operations(self):
        """Test user preferences storage and retrieval."""
        self.db_manager.initialize_database()

        # Create test user
        user_data = {
            'username': 'prefs_test_user',
            'email': '<EMAIL>',
            'password_hash': 'hash',
            'salt': 'salt',
            'is_admin': 0,
            'is_guest': 0,
            'registration_date': datetime.now().isoformat()
        }
        user_id = self.db_manager.create_user(user_data)

        # Test setting preferences
        preferences = {
            'weight_kg': 75.5,
            'default_transport_mode': 'e-bike',
            'theme': 'dark',
            'units': 'metric',
            'notifications_enabled': True
        }

        for key, value in preferences.items():
            result = self.db_manager.set_user_preference(user_id, key, value)
            self.assertTrue(result)

        # Test getting preferences
        for key, expected_value in preferences.items():
            actual_value = self.db_manager.get_user_preference(user_id, key)
            self.assertEqual(actual_value, expected_value)

        # Test getting all preferences
        all_prefs = self.db_manager.get_all_user_preferences(user_id)
        self.assertIsNotNone(all_prefs)
        self.assertEqual(len(all_prefs), len(preferences))

        # Test preference update
        result = self.db_manager.set_user_preference(user_id, 'weight_kg', 80.0)
        self.assertTrue(result)

        updated_weight = self.db_manager.get_user_preference(user_id, 'weight_kg')
        self.assertEqual(updated_weight, 80.0)

    def test_database_backup_and_restore(self):
        """Test database backup and restore functionality."""
        self.db_manager.initialize_database()

        # Create test data
        user_data = {
            'username': 'backup_test_user',
            'email': '<EMAIL>',
            'password_hash': 'hash',
            'salt': 'salt',
            'is_admin': 0,
            'is_guest': 0,
            'registration_date': datetime.now().isoformat()
        }
        user_id = self.db_manager.create_user(user_data)

        # Create backup
        backup_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        backup_file.close()

        try:
            result = self.db_manager.create_backup(backup_file.name)
            self.assertTrue(result)
            self.assertTrue(os.path.exists(backup_file.name))

            # Verify backup contains data
            with sqlite3.connect(backup_file.name) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM users")
                user_count = cursor.fetchone()[0]
                self.assertGreater(user_count, 0)

            # Test restore (create new database and restore from backup)
            new_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
            new_db.close()

            try:
                new_db_manager = DatabaseManager(new_db.name)
                result = new_db_manager.restore_from_backup(backup_file.name)
                self.assertTrue(result)

                # Verify restored data
                restored_user = new_db_manager.get_user_by_username('backup_test_user')
                self.assertIsNotNone(restored_user)
                self.assertEqual(restored_user['email'], '<EMAIL>')

            finally:
                if os.path.exists(new_db.name):
                    os.unlink(new_db.name)

        finally:
            if os.path.exists(backup_file.name):
                os.unlink(backup_file.name)

    def test_database_migrations(self):
        """Test database migration functionality."""
        self.db_manager.initialize_database()

        # Test migration tracking
        migration_version = '1.0.0'
        result = self.db_manager.record_migration(migration_version)
        self.assertTrue(result)

        # Verify migration was recorded
        applied_migrations = self.db_manager.get_applied_migrations()
        self.assertIn(migration_version, applied_migrations)

        # Test checking if migration is applied
        is_applied = self.db_manager.is_migration_applied(migration_version)
        self.assertTrue(is_applied)

        # Test checking non-applied migration
        is_not_applied = self.db_manager.is_migration_applied('2.0.0')
        self.assertFalse(is_not_applied)

    def test_database_performance(self):
        """Test database performance with bulk operations."""
        self.db_manager.initialize_database()

        # Create test user
        user_data = {
            'username': 'perf_test_user',
            'email': '<EMAIL>',
            'password_hash': 'hash',
            'salt': 'salt',
            'is_admin': 0,
            'is_guest': 0,
            'registration_date': datetime.now().isoformat()
        }
        user_id = self.db_manager.create_user(user_data)

        # Test bulk trip insertion
        import time
        start_time = time.time()

        trips_data = []
        for i in range(100):
            trip_data = {
                'user_id': user_id,
                'date': datetime.now().isoformat(),
                'distance': 5.0 + (i % 10),
                'duration': 15.0 + (i % 5),
                'co2_saved': 0.96 + (i % 3) * 0.1,
                'calories': 250 + (i % 20)
            }
            trips_data.append(trip_data)

        # Insert trips
        for trip_data in trips_data:
            self.db_manager.create_trip(trip_data)

        end_time = time.time()
        execution_time = end_time - start_time

        # Performance should be reasonable (less than 5 seconds for 100 trips)
        self.assertLess(execution_time, 5.0, "Bulk insertion took too long")

        # Verify all trips were inserted
        user_trips = self.db_manager.get_trips_by_user(user_id)
        self.assertEqual(len(user_trips), 100)

    def test_error_handling(self):
        """Test database error handling."""
        # Test operations on uninitialized database
        with self.assertRaises(Exception):
            self.db_manager.get_user_by_username('test_user')

        # Initialize database for further tests
        self.db_manager.initialize_database()

        # Test invalid user creation
        invalid_user_data = {}  # Missing required fields
        with self.assertRaises(Exception):
            self.db_manager.create_user(invalid_user_data)

        # Test operations on non-existent records
        non_existent_user = self.db_manager.get_user_by_id(99999)
        self.assertIsNone(non_existent_user)

        non_existent_trip = self.db_manager.get_trip_by_id(99999)
        self.assertIsNone(non_existent_trip)

        # Test invalid SQL operations
        with self.assertRaises(Exception):
            execute_query("INVALID SQL STATEMENT")

if __name__ == '__main__':
    unittest.main()
