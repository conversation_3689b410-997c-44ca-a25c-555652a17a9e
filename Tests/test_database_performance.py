#!/usr/bin/env python3
"""
Database Performance Tests
Tests database performance under various load conditions including
bulk operations, concurrent access, and optimization features.
"""

import os
import sys
import unittest
import tempfile
import time
import threading
import sqlite3
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

from Tests import EcoCycleTestCase, get_test_config
from core.database_manager import DatabaseManager

class TestDatabasePerformance(EcoCycleTestCase):
    """Performance tests for database operations."""
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        
        # Create temporary database
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        # Initialize database manager
        self.db_manager = DatabaseManager(self.temp_db.name)
        self.db_manager.initialize_database()
        
        # Create test user for performance tests
        self.test_user_data = {
            'username': 'perf_test_user',
            'email': '<EMAIL>',
            'password_hash': 'test_hash',
            'salt': 'test_salt',
            'is_admin': 0,
            'is_guest': 0,
            'registration_date': datetime.now().isoformat()
        }
        self.test_user_id = self.db_manager.create_user(self.test_user_data)
        
    def tearDown(self):
        """Clean up test fixtures."""
        super().tearDown()
        
        # Close database connections
        if hasattr(self.db_manager, 'connection_pool'):
            self.db_manager.close_all_connections()
        
        # Remove temporary database
        if os.path.exists(self.temp_db.name):
            try:
                os.unlink(self.temp_db.name)
            except OSError:
                pass
    
    def test_bulk_trip_insertion_performance(self):
        """Test performance of bulk trip insertions."""
        num_trips = 1000
        trips_data = []
        
        # Generate test trip data
        for i in range(num_trips):
            trip_data = {
                'user_id': self.test_user_id,
                'date': (datetime.now() - timedelta(days=i % 365)).isoformat(),
                'distance': 5.0 + (i % 20),
                'duration': 15.0 + (i % 30),
                'co2_saved': 0.96 + (i % 5) * 0.1,
                'calories': 250 + (i % 100)
            }
            trips_data.append(trip_data)
        
        # Measure insertion time
        start_time = time.time()
        
        trip_ids = []
        for trip_data in trips_data:
            trip_id = self.db_manager.create_trip(trip_data)
            trip_ids.append(trip_id)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Performance assertions
        self.assertEqual(len(trip_ids), num_trips)
        self.assertLess(execution_time, 10.0, f"Bulk insertion took {execution_time:.2f}s, should be under 10s")
        
        # Calculate operations per second
        ops_per_second = num_trips / execution_time
        self.assertGreater(ops_per_second, 100, f"Should achieve >100 ops/sec, got {ops_per_second:.1f}")
        
        print(f"Bulk insertion performance: {ops_per_second:.1f} trips/second")
    
    def test_bulk_query_performance(self):
        """Test performance of bulk data retrieval."""
        # Create test data
        num_trips = 500
        for i in range(num_trips):
            trip_data = {
                'user_id': self.test_user_id,
                'date': (datetime.now() - timedelta(days=i)).isoformat(),
                'distance': 5.0 + (i % 15),
                'duration': 20.0 + (i % 25),
                'co2_saved': 1.0 + (i % 3) * 0.2,
                'calories': 300 + (i % 80)
            }
            self.db_manager.create_trip(trip_data)
        
        # Test query performance
        start_time = time.time()
        
        # Retrieve all trips for user
        user_trips = self.db_manager.get_trips_by_user(self.test_user_id)
        
        end_time = time.time()
        query_time = end_time - start_time
        
        # Performance assertions
        self.assertEqual(len(user_trips), num_trips)
        self.assertLess(query_time, 2.0, f"Query took {query_time:.2f}s, should be under 2s")
        
        print(f"Bulk query performance: {len(user_trips)} trips retrieved in {query_time:.3f}s")
        
        # Test statistics calculation performance
        start_time = time.time()
        stats = self.db_manager.calculate_user_statistics(self.test_user_id)
        end_time = time.time()
        
        stats_time = end_time - start_time
        self.assertLess(stats_time, 1.0, f"Statistics calculation took {stats_time:.2f}s, should be under 1s")
        
        print(f"Statistics calculation: {stats_time:.3f}s")
    
    def test_concurrent_database_access(self):
        """Test database performance under concurrent access."""
        num_threads = 10
        operations_per_thread = 50
        
        def worker_function(thread_id):
            """Worker function for concurrent testing."""
            results = []
            
            for i in range(operations_per_thread):
                try:
                    # Create trip
                    trip_data = {
                        'user_id': self.test_user_id,
                        'date': datetime.now().isoformat(),
                        'distance': 5.0 + thread_id,
                        'duration': 20.0 + i,
                        'co2_saved': 1.0,
                        'calories': 300
                    }
                    trip_id = self.db_manager.create_trip(trip_data)
                    
                    # Read trip back
                    retrieved_trip = self.db_manager.get_trip_by_id(trip_id)
                    
                    results.append({
                        'thread_id': thread_id,
                        'operation': i,
                        'trip_id': trip_id,
                        'success': retrieved_trip is not None
                    })
                    
                except Exception as e:
                    results.append({
                        'thread_id': thread_id,
                        'operation': i,
                        'error': str(e),
                        'success': False
                    })
            
            return results
        
        # Execute concurrent operations
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(worker_function, i) for i in range(num_threads)]
            all_results = []
            
            for future in as_completed(futures):
                thread_results = future.result()
                all_results.extend(thread_results)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Analyze results
        successful_operations = sum(1 for result in all_results if result['success'])
        total_operations = num_threads * operations_per_thread
        
        self.assertEqual(successful_operations, total_operations, 
                        f"Expected {total_operations} successful operations, got {successful_operations}")
        
        ops_per_second = total_operations / total_time
        self.assertGreater(ops_per_second, 50, f"Concurrent performance should be >50 ops/sec, got {ops_per_second:.1f}")
        
        print(f"Concurrent access performance: {ops_per_second:.1f} ops/second with {num_threads} threads")
    
    def test_database_indexing_performance(self):
        """Test performance improvements from database indexing."""
        # Create large dataset
        num_users = 100
        trips_per_user = 50
        
        # Create multiple users
        user_ids = []
        for i in range(num_users):
            user_data = {
                'username': f'user_{i}',
                'email': f'user_{i}@example.com',
                'password_hash': 'hash',
                'salt': 'salt',
                'is_admin': 0,
                'is_guest': 0,
                'registration_date': datetime.now().isoformat()
            }
            user_id = self.db_manager.create_user(user_data)
            user_ids.append(user_id)
        
        # Create trips for each user
        for user_id in user_ids:
            for j in range(trips_per_user):
                trip_data = {
                    'user_id': user_id,
                    'date': (datetime.now() - timedelta(days=j)).isoformat(),
                    'distance': 5.0 + (j % 10),
                    'duration': 20.0,
                    'co2_saved': 1.0,
                    'calories': 300
                }
                self.db_manager.create_trip(trip_data)
        
        # Test query performance with indexing
        test_user_id = user_ids[50]  # Middle user
        
        start_time = time.time()
        user_trips = self.db_manager.get_trips_by_user(test_user_id)
        end_time = time.time()
        
        indexed_query_time = end_time - start_time
        
        self.assertEqual(len(user_trips), trips_per_user)
        self.assertLess(indexed_query_time, 0.5, f"Indexed query took {indexed_query_time:.3f}s, should be under 0.5s")
        
        print(f"Indexed query performance: {indexed_query_time:.3f}s for {len(user_trips)} trips")
        
        # Test date range queries
        start_date = (datetime.now() - timedelta(days=30)).isoformat()
        end_date = datetime.now().isoformat()
        
        start_time = time.time()
        recent_trips = self.db_manager.get_trips_by_date_range(test_user_id, start_date, end_date)
        end_time = time.time()
        
        date_query_time = end_time - start_time
        self.assertLess(date_query_time, 0.3, f"Date range query took {date_query_time:.3f}s, should be under 0.3s")
        
        print(f"Date range query performance: {date_query_time:.3f}s")
    
    def test_memory_usage_performance(self):
        """Test memory usage during large operations."""
        import psutil
        import gc
        
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Perform memory-intensive operations
        num_operations = 1000
        large_data_operations = []
        
        for i in range(num_operations):
            # Create trip with large route data
            large_route_data = {
                'waypoints': [{'lat': 40.7128 + i * 0.001, 'lng': -74.0060 + i * 0.001} for _ in range(100)],
                'elevation_profile': [100 + j for j in range(100)],
                'traffic_data': {'congestion_level': i % 5, 'estimated_time': 30 + i % 20}
            }
            
            trip_data = {
                'user_id': self.test_user_id,
                'date': datetime.now().isoformat(),
                'distance': 10.0,
                'duration': 30.0,
                'co2_saved': 2.0,
                'calories': 500,
                'route_data': str(large_route_data)  # Convert to string for storage
            }
            
            trip_id = self.db_manager.create_trip(trip_data)
            large_data_operations.append(trip_id)
        
        # Force garbage collection
        gc.collect()
        
        # Get peak memory usage
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = peak_memory - initial_memory
        
        # Memory usage should be reasonable (less than 100MB increase for this test)
        self.assertLess(memory_increase, 100, f"Memory usage increased by {memory_increase:.1f}MB, should be under 100MB")
        
        print(f"Memory usage: {initial_memory:.1f}MB -> {peak_memory:.1f}MB (increase: {memory_increase:.1f}MB)")
        
        # Test memory cleanup after operations
        large_data_operations.clear()
        gc.collect()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_cleanup = peak_memory - final_memory
        
        print(f"Memory after cleanup: {final_memory:.1f}MB (cleaned up: {memory_cleanup:.1f}MB)")
    
    def test_database_optimization_features(self):
        """Test database optimization features."""
        # Test VACUUM operation performance
        start_time = time.time()
        self.db_manager.vacuum_database()
        vacuum_time = time.time() - start_time
        
        self.assertLess(vacuum_time, 5.0, f"VACUUM operation took {vacuum_time:.2f}s, should be under 5s")
        
        # Test ANALYZE operation performance
        start_time = time.time()
        self.db_manager.analyze_database()
        analyze_time = time.time() - start_time
        
        self.assertLess(analyze_time, 2.0, f"ANALYZE operation took {analyze_time:.2f}s, should be under 2s")
        
        # Test database size optimization
        initial_size = os.path.getsize(self.temp_db.name)
        
        # Create and delete some data to create fragmentation
        temp_trips = []
        for i in range(100):
            trip_data = {
                'user_id': self.test_user_id,
                'date': datetime.now().isoformat(),
                'distance': 5.0,
                'duration': 20.0,
                'co2_saved': 1.0,
                'calories': 300
            }
            trip_id = self.db_manager.create_trip(trip_data)
            temp_trips.append(trip_id)
        
        # Delete half the trips
        for trip_id in temp_trips[::2]:
            self.db_manager.delete_trip(trip_id)
        
        # Optimize database
        self.db_manager.vacuum_database()
        
        optimized_size = os.path.getsize(self.temp_db.name)
        
        print(f"Database size: {initial_size} -> {optimized_size} bytes")
        
        # Test connection pool performance
        start_time = time.time()
        connections = []
        
        for i in range(10):
            conn = self.db_manager.get_connection()
            connections.append(conn)
        
        connection_time = time.time() - start_time
        
        # Close connections
        for conn in connections:
            conn.close()
        
        self.assertLess(connection_time, 1.0, f"Connection pool operations took {connection_time:.3f}s, should be under 1s")
        
        print(f"Connection pool performance: {connection_time:.3f}s for 10 connections")

if __name__ == '__main__':
    unittest.main()
