#!/usr/bin/env python3
"""
EcoCycle - Comprehensive Performance Testing Module
Tests load testing, memory usage monitoring, and API response times.
"""

import os
import sys
import unittest
import tempfile
import time
import threading
import psutil
import gc
import json
import requests
import multiprocessing
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from memory_profiler import profile
import tracemalloc

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

from Tests import EcoCycleTestCase, get_test_config
from auth.user_management.user_manager import UserManager
from core.database_manager import DatabaseManager
from web.web_app import app
from web.api_endpoints import api

class TestPerformanceComprehensive(EcoCycleTestCase):
    """Comprehensive performance tests for EcoCycle application."""
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        
        # Create temporary database
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        # Initialize components
        self.db_manager = DatabaseManager(self.temp_db.name)
        self.db_manager.initialize_database()
        self.user_manager = UserManager()
        
        # Flask test client
        app.config['TESTING'] = True
        self.client = app.test_client()
        
        # Performance metrics storage
        self.performance_metrics = {
            'load_test_results': [],
            'memory_usage_results': [],
            'api_response_times': [],
            'concurrent_user_results': []
        }
        
        # Create test users for load testing
        self.test_users = self._create_test_users(50)
        
    def tearDown(self):
        """Clean up test fixtures."""
        super().tearDown()
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
    
    def _create_test_users(self, count: int) -> list:
        """Create test users for load testing."""
        test_users = []
        for i in range(count):
            user_data = {
                'username': f'load_test_user_{i}',
                'email': f'load_test_{i}@example.com',
                'password_hash': 'test_hash',
                'salt': 'test_salt',
                'is_admin': 0,
                'is_guest': 0,
                'registration_date': datetime.now().isoformat()
            }
            user_id = self.db_manager.create_user(user_data)
            test_users.append({'id': user_id, 'data': user_data})
        return test_users
    
    def test_load_testing_multiple_users(self):
        """Test application performance under multiple concurrent users."""
        num_concurrent_users = 20
        operations_per_user = 10
        
        def simulate_user_session(user_index):
            """Simulate a complete user session."""
            results = []
            start_time = time.time()
            
            try:
                # Simulate login
                login_start = time.time()
                with self.client.session_transaction() as sess:
                    sess['username'] = f'load_test_user_{user_index}'
                login_time = time.time() - login_start
                
                # Simulate various operations
                for op in range(operations_per_user):
                    op_start = time.time()
                    
                    # Simulate dashboard access
                    response = self.client.get('/')
                    dashboard_time = time.time() - op_start
                    
                    # Simulate trip logging
                    trip_start = time.time()
                    trip_data = {
                        'date': datetime.now().isoformat(),
                        'distance': 5.0 + (op % 10),
                        'duration': 20.0 + (op % 15),
                        'co2_saved': 1.0,
                        'calories': 300
                    }
                    trip_response = self.client.post('/api/trips', 
                                                   json=trip_data,
                                                   content_type='application/json')
                    trip_time = time.time() - trip_start
                    
                    # Simulate statistics view
                    stats_start = time.time()
                    stats_response = self.client.get('/api/statistics')
                    stats_time = time.time() - stats_start
                    
                    results.append({
                        'user_index': user_index,
                        'operation': op,
                        'login_time': login_time if op == 0 else 0,
                        'dashboard_time': dashboard_time,
                        'trip_time': trip_time,
                        'stats_time': stats_time,
                        'total_time': time.time() - op_start
                    })
                
                session_time = time.time() - start_time
                return {
                    'user_index': user_index,
                    'session_time': session_time,
                    'operations': results,
                    'success': True
                }
                
            except Exception as e:
                return {
                    'user_index': user_index,
                    'error': str(e),
                    'success': False
                }
        
        # Execute concurrent user sessions
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_concurrent_users) as executor:
            futures = [executor.submit(simulate_user_session, i) 
                      for i in range(num_concurrent_users)]
            
            results = []
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
        
        total_time = time.time() - start_time
        
        # Analyze results
        successful_sessions = [r for r in results if r.get('success', False)]
        failed_sessions = [r for r in results if not r.get('success', False)]
        
        # Performance assertions
        success_rate = len(successful_sessions) / len(results)
        self.assertGreater(success_rate, 0.8, f"Success rate should be >80%, got {success_rate:.2%}")
        
        avg_session_time = sum(r['session_time'] for r in successful_sessions) / len(successful_sessions)
        self.assertLess(avg_session_time, 30.0, f"Average session time should be <30s, got {avg_session_time:.2f}s")
        
        # Store results
        self.performance_metrics['load_test_results'] = {
            'concurrent_users': num_concurrent_users,
            'operations_per_user': operations_per_user,
            'total_time': total_time,
            'success_rate': success_rate,
            'avg_session_time': avg_session_time,
            'successful_sessions': len(successful_sessions),
            'failed_sessions': len(failed_sessions)
        }
        
        print(f"Load test completed: {success_rate:.2%} success rate, "
              f"{avg_session_time:.2f}s avg session time")
    
    def test_memory_usage_monitoring(self):
        """Test memory usage under various load conditions."""
        # Start memory tracking
        tracemalloc.start()
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        memory_snapshots = []
        
        def take_memory_snapshot(label):
            """Take a memory snapshot with label."""
            current_memory = process.memory_info().rss / 1024 / 1024
            memory_percent = process.memory_percent()
            snapshot = tracemalloc.take_snapshot()
            top_stats = snapshot.statistics('lineno')
            
            memory_snapshots.append({
                'label': label,
                'memory_mb': current_memory,
                'memory_percent': memory_percent,
                'memory_increase': current_memory - initial_memory,
                'top_memory_lines': len(top_stats[:10])
            })
            
            return current_memory
        
        # Baseline measurement
        take_memory_snapshot('baseline')
        
        # Memory-intensive operations
        large_data_sets = []
        
        # Test 1: Large data creation
        for i in range(1000):
            large_data = {
                'trip_id': i,
                'data': [j for j in range(100)],
                'metadata': {'created': datetime.now().isoformat()}
            }
            large_data_sets.append(large_data)
        
        take_memory_snapshot('after_large_data_creation')
        
        # Test 2: Database operations
        for i in range(500):
            trip_data = {
                'user_id': 1,
                'date': datetime.now().isoformat(),
                'distance': 5.0 + i,
                'duration': 20.0 + i,
                'co2_saved': 1.0,
                'calories': 300
            }
            self.db_manager.create_trip(trip_data)
        
        take_memory_snapshot('after_database_operations')
        
        # Test 3: Cleanup and garbage collection
        del large_data_sets
        gc.collect()
        
        take_memory_snapshot('after_cleanup')
        
        # Stop memory tracking
        tracemalloc.stop()
        
        # Analyze memory usage
        max_memory = max(s['memory_mb'] for s in memory_snapshots)
        final_memory = memory_snapshots[-1]['memory_mb']
        memory_growth = final_memory - initial_memory
        
        # Performance assertions
        self.assertLess(max_memory, 500, f"Peak memory usage should be <500MB, got {max_memory:.2f}MB")
        self.assertLess(memory_growth, 100, f"Memory growth should be <100MB, got {memory_growth:.2f}MB")
        
        # Store results
        self.performance_metrics['memory_usage_results'] = {
            'initial_memory_mb': initial_memory,
            'peak_memory_mb': max_memory,
            'final_memory_mb': final_memory,
            'memory_growth_mb': memory_growth,
            'snapshots': memory_snapshots
        }
        
        print(f"Memory test completed: Peak {max_memory:.2f}MB, Growth {memory_growth:.2f}MB")
    
    def test_api_response_time_testing(self):
        """Test API endpoint response times under load."""
        endpoints_to_test = [
            {'method': 'GET', 'url': '/', 'name': 'dashboard'},
            {'method': 'GET', 'url': '/api/statistics', 'name': 'statistics'},
            {'method': 'POST', 'url': '/api/trips', 'name': 'create_trip'},
            {'method': 'GET', 'url': '/api/routes', 'name': 'get_routes'},
        ]
        
        response_times = {}
        
        for endpoint in endpoints_to_test:
            endpoint_times = []
            
            # Test each endpoint multiple times
            for i in range(50):
                start_time = time.time()
                
                try:
                    if endpoint['method'] == 'GET':
                        with self.client.session_transaction() as sess:
                            sess['username'] = 'test_user'
                        response = self.client.get(endpoint['url'])
                    elif endpoint['method'] == 'POST':
                        with self.client.session_transaction() as sess:
                            sess['username'] = 'test_user'
                        test_data = {
                            'date': datetime.now().isoformat(),
                            'distance': 5.0,
                            'duration': 20.0,
                            'co2_saved': 1.0,
                            'calories': 300
                        }
                        response = self.client.post(endpoint['url'], 
                                                  json=test_data,
                                                  content_type='application/json')
                    
                    response_time = time.time() - start_time
                    endpoint_times.append({
                        'response_time': response_time,
                        'status_code': response.status_code,
                        'success': response.status_code < 400
                    })
                    
                except Exception as e:
                    endpoint_times.append({
                        'response_time': time.time() - start_time,
                        'error': str(e),
                        'success': False
                    })
            
            # Calculate statistics
            successful_requests = [t for t in endpoint_times if t.get('success', False)]
            if successful_requests:
                avg_response_time = sum(t['response_time'] for t in successful_requests) / len(successful_requests)
                max_response_time = max(t['response_time'] for t in successful_requests)
                min_response_time = min(t['response_time'] for t in successful_requests)
                success_rate = len(successful_requests) / len(endpoint_times)
                
                response_times[endpoint['name']] = {
                    'avg_response_time': avg_response_time,
                    'max_response_time': max_response_time,
                    'min_response_time': min_response_time,
                    'success_rate': success_rate,
                    'total_requests': len(endpoint_times)
                }
                
                # Performance assertions
                self.assertLess(avg_response_time, 2.0, 
                              f"{endpoint['name']} avg response time should be <2s, got {avg_response_time:.3f}s")
                self.assertGreater(success_rate, 0.95, 
                                 f"{endpoint['name']} success rate should be >95%, got {success_rate:.2%}")
        
        # Store results
        self.performance_metrics['api_response_times'] = response_times
        
        print("API response time testing completed:")
        for endpoint, metrics in response_times.items():
            print(f"  {endpoint}: {metrics['avg_response_time']:.3f}s avg, "
                  f"{metrics['success_rate']:.2%} success rate")

if __name__ == '__main__':
    unittest.main()
