#!/usr/bin/env python3
"""
Quick CI Test Script for EcoCycle
Tests basic functionality without heavy dependencies
"""

import sys
import os
import subprocess
import importlib.util

def test_python_syntax():
    """Test Python syntax of main files"""
    print("Testing Python syntax...")
    main_files = ['main.py', 'cli.py', 'setup.py']
    
    for file in main_files:
        if os.path.exists(file):
            try:
                result = subprocess.run([sys.executable, '-m', 'py_compile', file], 
                                      capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    print(f"✓ {file} syntax OK")
                else:
                    print(f"✗ {file} syntax error: {result.stderr}")
                    return False
            except subprocess.TimeoutExpired:
                print(f"✗ {file} syntax check timed out")
                return False
        else:
            print(f"⚠ {file} not found")
    
    return True

def test_imports():
    """Test basic imports"""
    print("Testing basic imports...")
    
    try:
        # Test main module import
        if os.path.exists('main.py'):
            spec = importlib.util.spec_from_file_location("main", "main.py")
            if spec and spec.loader:
                print("✓ main.py can be imported")
            else:
                print("✗ main.py import failed")
                return False
        
        # Test CLI module import
        if os.path.exists('cli.py'):
            spec = importlib.util.spec_from_file_location("cli", "cli.py")
            if spec and spec.loader:
                print("✓ cli.py can be imported")
            else:
                print("✗ cli.py import failed")
                return False
                
    except Exception as e:
        print(f"✗ Import test failed: {e}")
        return False
    
    return True

def test_basic_functionality():
    """Test basic functionality without heavy operations"""
    print("Testing basic functionality...")
    
    try:
        # Test that main.py can be executed with --help
        if os.path.exists('main.py'):
            result = subprocess.run([sys.executable, 'main.py', '--help'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0 or 'usage' in result.stdout.lower() or 'help' in result.stdout.lower():
                print("✓ main.py --help works")
            else:
                print(f"⚠ main.py --help returned: {result.returncode}")
        
        # Test CLI help
        if os.path.exists('cli.py'):
            result = subprocess.run([sys.executable, 'cli.py', '--help'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0 or 'usage' in result.stdout.lower() or 'help' in result.stdout.lower():
                print("✓ cli.py --help works")
            else:
                print(f"⚠ cli.py --help returned: {result.returncode}")
                
    except subprocess.TimeoutExpired:
        print("⚠ Basic functionality test timed out")
        return False
    except Exception as e:
        print(f"⚠ Basic functionality test failed: {e}")
        return False
    
    return True

def main():
    """Run all quick tests"""
    print("Running Quick CI Tests for EcoCycle")
    print("=" * 40)
    
    tests = [
        test_python_syntax,
        test_imports,
        test_basic_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            print()
    
    print("=" * 40)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All quick tests passed!")
        return 0
    else:
        print("✗ Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
