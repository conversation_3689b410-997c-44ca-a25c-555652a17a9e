#!/usr/bin/env python3
"""
Local CI Test Runner for EcoCycle
Simulates CI/CD pipeline locally for faster development
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def run_command(cmd, description, timeout=300):
    """Run a command with timeout and error handling"""
    print(f"\n{'='*50}")
    print(f"Running: {description}")
    print(f"Command: {cmd}")
    print(f"{'='*50}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout
        )
        
        elapsed = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✓ {description} PASSED ({elapsed:.1f}s)")
            if result.stdout:
                print("STDOUT:", result.stdout[:500])
            return True
        else:
            print(f"✗ {description} FAILED ({elapsed:.1f}s)")
            print("STDERR:", result.stderr[:500])
            print("STDOUT:", result.stdout[:500])
            return False
            
    except subprocess.TimeoutExpired:
        print(f"✗ {description} TIMED OUT after {timeout}s")
        return False
    except Exception as e:
        print(f"✗ {description} ERROR: {e}")
        return False

def main():
    """Run local CI pipeline"""
    print("EcoCycle Local CI Pipeline")
    print("=" * 50)
    
    # Change to project root
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    tests = [
        # Quick syntax check
        ("python scripts/quick_ci_test.py", "Quick CI Tests", 60),
        
        # Linting (main files only)
        ("flake8 main.py cli.py setup.py --count --select=E9,F63,F7,F82", "Syntax Check", 30),
        
        # Security scan (main files only)
        ("bandit -r main.py cli.py setup.py --format txt", "Security Scan", 60),
        
        # Basic tests
        ("python -c \"import main; print('Main import OK')\"", "Main Import Test", 30),
        ("python -c \"import cli; print('CLI import OK')\"", "CLI Import Test", 30),
        
        # Help commands
        ("python main.py --help", "Main Help Test", 30),
        ("python cli.py --help", "CLI Help Test", 30),
    ]
    
    passed = 0
    total = len(tests)
    
    for cmd, description, timeout in tests:
        if run_command(cmd, description, timeout):
            passed += 1
    
    print(f"\n{'='*50}")
    print(f"LOCAL CI RESULTS: {passed}/{total} tests passed")
    print(f"{'='*50}")
    
    if passed == total:
        print("✓ All local CI tests passed! Ready for push.")
        return 0
    else:
        print("✗ Some tests failed. Fix issues before pushing.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
